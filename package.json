{"name": "playwright-langchain-bot", "version": "1.0.0", "description": "A web automation bot using <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>", "main": "dist/index.js", "type": "module", "scripts": {"start": "node --inspect=9229 dist/index.js", "dev": "ts-node --esm src/index.ts", "build": "tsc", "lint": "eslint . --ext .ts,.tsx --fix", "lint:check": "eslint . --ext .ts,.tsx", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "type-check": "tsc --noEmit", "test": "node --experimental-vm-modules node_modules/jest/bin/jest.js", "test:coverage": "node --experimental-vm-modules node_modules/jest/bin/jest.js --coverage", "test:watch": "node --experimental-vm-modules node_modules/jest/bin/jest.js --watch", "test:automation": "node --experimental-vm-modules node_modules/jest/bin/jest.js tests/automation/", "test:safe": "node --experimental-vm-modules node_modules/jest/bin/jest.js tests/automation/progress.test.ts tests/automation/milestones.test.ts tests/automation/machine.test.ts tests/core/actions/actionExtractor.test.ts tests/core/browserExecutor.test.ts", "test:working": "node --experimental-vm-modules node_modules/jest/bin/jest.js tests/automation/ tests/core/elements/", "audit": "npm audit --production", "audit:fix": "npm audit fix", "circular": "madge --circular --extensions ts src/", "prepare": "husky install", "precommit": "lint-staged", "ci": "npm run lint:check && npm run type-check && npm run test:coverage && npm run build", "release": "changeset publish"}, "dependencies": {"@google/generative-ai": "0.22.0", "@langchain/langgraph": "0.2.49", "@langchain/ollama": "0.2.0", "@playwright/test": "1.50.1", "cheerio": "1.0.0", "cssesc": "3.0.0", "dotenv-flow": "4.1.0", "langchain": "0.1.4", "node-fetch": "2.7.0", "playwright": "1.42.0", "zod": "3.21.4"}, "devDependencies": {"@changesets/cli": "2.27.1", "@commitlint/cli": "18.4.3", "@commitlint/config-conventional": "18.4.3", "@eslint/js": "8.56.0", "@jest/globals": "29.6.4", "@types/cssesc": "3.0.2", "@types/domhandler": "2.4.5", "@types/jest": "29.5.4", "@types/node": "18.16.19", "@types/node-fetch": "2.6.12", "@typescript-eslint/eslint-plugin": "6.14.0", "@typescript-eslint/parser": "6.14.0", "eslint": "8.56.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.1.0", "husky": "8.0.3", "jest": "29.6.4", "jest-mock": "29.6.3", "lint-staged": "15.2.0", "madge": "6.1.0", "prettier": "3.1.1", "ts-jest": "29.1.2", "ts-node": "10.9.2", "typescript": "5.8.2"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"], "*.{js,jsx,json,md}": ["prettier --write"]}}