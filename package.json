{"name": "playwright-langchain-bot", "version": "1.0.0", "description": "A web automation bot using <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>", "main": "dist/index.js", "type": "module", "scripts": {"start": "node --inspect=9229 dist/index.js", "dev": "ts-node --esm src/index.ts", "build": "tsc", "lint": "eslint . --ext .ts", "test": "node --experimental-vm-modules node_modules/jest/bin/jest.js", "test:automation": "node --experimental-vm-modules node_modules/jest/bin/jest.js src/__tests__/automation/", "test:safe": "node --experimental-vm-modules node_modules/jest/bin/jest.js src/__tests__/automation/progress.test.ts src/__tests__/automation/milestones.test.ts src/__tests__/automation/machine.test.ts src/__tests__/core/actions/actionExtractor.test.ts src/__tests__/core/browserExecutor.test.ts", "test:working": "node --experimental-vm-modules node_modules/jest/bin/jest.js src/__tests__/automation/ src/__tests__/core/elements/"}, "dependencies": {"@google/generative-ai": "^0.22.0", "@langchain/langgraph": "^0.2.49", "@langchain/ollama": "^0.2.0", "@playwright/test": "^1.50.1", "cheerio": "^1.0.0", "cssesc": "^3.0.0", "dotenv": "^16.4.7", "langchain": "^0.1.4", "node-fetch": "^2.7.0", "playwright": "^1.42.0", "zod": "^3.21.4"}, "devDependencies": {"@jest/globals": "^29.6.4", "@types/cssesc": "^3.0.2", "@types/domhandler": "^2.4.5", "@types/jest": "^29.5.4", "@types/node": "^18.16.19", "@types/node-fetch": "^2.6.12", "eslint": "^8.56.0", "jest": "^29.6.4", "jest-mock": "^29.6.3", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5.8.2"}}